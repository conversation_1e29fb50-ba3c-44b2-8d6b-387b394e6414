/**
 * ModulePersistenceClient
 * Client-side wrapper for JsonPersistenceService that works through IPC
 * Provides persistence interface using JSON files instead of unreliable SQLite
 */

class ModulePersistenceClient {
    constructor(logger) {
        this.logger = logger || console;
        this.isInitialized = false;
        this.persistenceMode = 'json'; // Use JSON persistence by default
        
        // Cache for frequently accessed data
        this.moduleCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
        this.lastCacheUpdate = 0;
    }

    /**
     * Initialize the client
     * @returns {Promise<boolean>}
     */
    async initialize() {
        try {
            this.logger.info('ModulePersistenceClient', 'Initializing JSON-based persistence...');

            // Check if JSON persistence is available through IPC
            if (!window.electronAPI || !window.electronAPI.jsonPersistence) {
                this.logger.warn('ModulePersistenceClient', 'JSON persistence API not available, using localStorage fallback');
                return await this.initializeLocalStorageFallback();
            }

            // Test JSON persistence connection
            const testResult = await window.electronAPI.jsonPersistence.getStats();
            if (testResult && testResult.isInitialized) {
                this.logger.info('ModulePersistenceClient', 'JSON persistence service is ready');
                
                // Load initial cache
                await this.refreshCache();
                this.isInitialized = true;
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', 'JSON persistence service not initialized, using localStorage fallback');
                return await this.initializeLocalStorageFallback();
            }

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to initialize:', error);
            return await this.initializeLocalStorageFallback();
        }
    }

    async initializeLocalStorageFallback() {
        try {
            this.logger.info('ModulePersistenceClient', 'Initializing localStorage fallback...');
            this.persistenceMode = 'localStorage';
            
            // Load modules from localStorage
            await this.loadFromLocalStorage();
            
            this.isInitialized = true;
            this.logger.info('ModulePersistenceClient', 'localStorage fallback initialized successfully');
            return true;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'localStorage fallback initialization failed:', error);
            return false;
        }
    }

    async loadFromLocalStorage() {
        try {
            this.logger.info('ModulePersistenceClient', 'Loading modules from localStorage...');
            
            const moduleKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('coredesk_module_')) {
                    moduleKeys.push(key);
                }
            }

            this.logger.info('ModulePersistenceClient', `Found ${moduleKeys.length} modules in localStorage`);
            
            // Refresh cache with localStorage data
            this.moduleCache.clear();
            this.lastCacheUpdate = Date.now();

            for (const key of moduleKeys) {
                try {
                    const moduleData = JSON.parse(localStorage.getItem(key));
                    if (moduleData && moduleData.moduleId) {
                        this.moduleCache.set(moduleData.moduleId, {
                            ...moduleData,
                            source: 'localStorage'
                        });
                        this.logger.debug('ModulePersistenceClient', `Loaded module ${moduleData.moduleId} from localStorage`);
                    }
                } catch (parseError) {
                    this.logger.warn('ModulePersistenceClient', `Failed to parse module data for ${key}:`, parseError);
                }
            }

            this.logger.info('ModulePersistenceClient', `Loaded ${this.moduleCache.size} modules from localStorage`);
            return true;

        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to load from localStorage:', error);
            return false;
        }
    }


    /**
     * Register a module as installed
     * @param {Object} moduleData - Module data to register
     * @returns {Promise<boolean>}
     */
    async registerInstalledModule(moduleData) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            if (!moduleId || !name || !version || !installPath) {
                throw new Error('Missing required module data');
            }

            this.logger.info('ModulePersistenceClient', `Registering module: ${moduleId}@${version} (mode: ${this.persistenceMode})`);

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const result = await window.electronAPI.jsonPersistence.registerModule({
                        moduleId,
                        name,
                        version,
                        status,
                        installPath,
                        manifestData
                    });
                    
                    if (result) {
                        this.logger.info('ModulePersistenceClient', `Module registered successfully with JSON persistence: ${moduleId}`);
                        await this.refreshCache();
                        return true;
                    } else {
                        throw new Error('JSON persistence registration failed');
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence failed for ${moduleId}, falling back to localStorage:`, jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage persistence fallback
            if (this.persistenceMode === 'localStorage') {
                return this.registerModuleInLocalStorage(moduleData);
            }

            throw new Error('No valid persistence mode available');
            
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to register module ${moduleData?.moduleId}:`, error);
            return false;
        }
    }

    registerModuleInLocalStorage(moduleData) {
        try {
            const { moduleId, name, version, installPath, manifestData, status = 'active' } = moduleData;
            
            const storageKey = `coredesk_module_${moduleId}`;
            const moduleRecord = {
                moduleId,
                name,
                version,
                status,
                installPath,
                manifestData,
                installedAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                source: 'localStorage'
            };

            localStorage.setItem(storageKey, JSON.stringify(moduleRecord));
            
            // Update cache immediately
            this.moduleCache.set(moduleId, moduleRecord);
            this.lastCacheUpdate = Date.now();
            
            this.logger.info('ModulePersistenceClient', `Registered module in localStorage: ${moduleId}`);
            return true;
            
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to register module in localStorage:`, error);
            return false;
        }
    }

    /**
     * Unregister a module
     * @param {string} moduleId - Module ID to unregister
     * @returns {Promise<boolean>}
     */
    async unregisterModule(moduleId) {
        try {
            if (!this.isInitialized) {
                this.logger.warn('ModulePersistenceClient', `Cannot unregister module ${moduleId}: Client not initialized`);
                return false;
            }

            this.logger.info('ModulePersistenceClient', `Unregistering module: ${moduleId}`);

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const result = await window.electronAPI.jsonPersistence.unregisterModule(moduleId);
                    
                    if (result) {
                        this.logger.info('ModulePersistenceClient', `Successfully unregistered module from JSON persistence: ${moduleId}`);
                        
                        // Remove from localStorage as well
                        const storageKey = `coredesk_module_${moduleId}`;
                        localStorage.removeItem(storageKey);
                        
                        // Remove from cache
                        this.moduleCache.delete(moduleId);
                        
                        return true;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence unregister failed for ${moduleId}:`, jsonError);
                }
            }

            // Try database persistence as fallback
            if (window.electronAPI && window.electronAPI.database) {
                try {
                    const sql = 'DELETE FROM installed_modules WHERE module_id = ?';
                    const result = await window.electronAPI.database.execute(sql, [moduleId]);
                    
                    if (result && result.success) {
                        this.logger.info('ModulePersistenceClient', `Successfully unregistered module from database: ${moduleId}`);
                        
                        // Refresh cache
                        await this.refreshCache();
                        
                        return true;
                    } else {
                        this.logger.warn('ModulePersistenceClient', `Database unregister failed: ${result?.error || 'Unknown error'}`);
                    }
                } catch (dbError) {
                    this.logger.warn('ModulePersistenceClient', `Database unregister failed for ${moduleId}:`, dbError);
                }
            }

            // Fallback to localStorage cleanup
            if (this.persistenceMode === 'localStorage') {
                const storageKey = `coredesk_module_${moduleId}`;
                localStorage.removeItem(storageKey);
                this.moduleCache.delete(moduleId);
                this.logger.info('ModulePersistenceClient', `Removed module from localStorage: ${moduleId}`);
                return true;
            }

            this.logger.error('ModulePersistenceClient', `All unregistration methods failed for module: ${moduleId}`);
            return false;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to unregister module ${moduleId}:`, error);
            return false;
        }
    }

    /**
     * Get all installed modules
     * @returns {Promise<Array>}
     */
    async getInstalledModules() {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Use cache if available and fresh
            if (this.isCacheValid()) {
                return Array.from(this.moduleCache.values());
            }

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const modules = await window.electronAPI.jsonPersistence.getInstalledModules();
                    
                    if (modules && Array.isArray(modules)) {
                        // Update cache
                        this.updateCache(modules);
                        return modules;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', 'JSON persistence query failed, falling back to localStorage:', jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage persistence fallback
            if (this.persistenceMode === 'localStorage') {
                await this.loadFromLocalStorage();
                return Array.from(this.moduleCache.values());
            }

            this.logger.warn('ModulePersistenceClient', 'No valid persistence mode available');
            return [];
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get installed modules:', error);
            return [];
        }
    }

    /**
     * Get a specific installed module
     * @param {string} moduleId - Module ID
     * @returns {Promise<Object|null>}
     */
    async getInstalledModule(moduleId) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            // Check cache first
            if (this.isCacheValid() && this.moduleCache.has(moduleId)) {
                return this.moduleCache.get(moduleId);
            }

            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const module = await window.electronAPI.jsonPersistence.getModule(moduleId);
                    
                    if (module) {
                        // Update cache entry
                        this.moduleCache.set(moduleId, module);
                        return module;
                    }
                    return null;
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', `JSON persistence query failed for ${moduleId}, checking localStorage:`, jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage fallback
            if (this.persistenceMode === 'localStorage') {
                const storageKey = `coredesk_module_${moduleId}`;
                const moduleData = localStorage.getItem(storageKey);
                
                if (moduleData) {
                    try {
                        const parsedModule = JSON.parse(moduleData);
                        this.moduleCache.set(moduleId, parsedModule);
                        return parsedModule;
                    } catch (parseError) {
                        this.logger.warn('ModulePersistenceClient', `Failed to parse module data for ${moduleId}:`, parseError);
                    }
                }
            }
            
            return null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to get module ${moduleId}:`, error);
            return null;
        }
    }

    /**
     * Check if a module is installed
     * @param {string} moduleId - Module ID
     * @returns {Promise<boolean>}
     */
    async isModuleInstalled(moduleId) {
        try {
            const module = await this.getInstalledModule(moduleId);
            return module !== null;
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to check if module ${moduleId} is installed:`, error);
            return false;
        }
    }

    /**
     * Update module status
     * @param {string} moduleId - Module ID
     * @param {string} status - New status ('active' or 'inactive')
     * @returns {Promise<boolean>}
     */
    async updateModuleStatus(moduleId, status) {
        try {
            if (!this.isInitialized) {
                throw new Error('Client not initialized');
            }

            if (!['active', 'inactive'].includes(status)) {
                throw new Error('Invalid status. Must be "active" or "inactive"');
            }

            this.logger.info('ModulePersistenceClient', `Updating module ${moduleId} status to: ${status}`);

            const sql = `
                UPDATE installed_modules 
                SET status = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE module_id = ?
            `;
            
            const result = await window.electronAPI.database.execute(sql, [status, moduleId]);
            
            if (result.success) {
                this.logger.info('ModulePersistenceClient', `Successfully updated module ${moduleId} status`);
                
                // Refresh cache
                await this.refreshCache();
                
                return true;
            } else {
                this.logger.warn('ModulePersistenceClient', `Failed to update module status: ${result.error}`);
                return false;
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', `Failed to update module ${moduleId} status:`, error);
            return false;
        }
    }

    /**
     * Get active modules only
     * @returns {Promise<Array>}
     */
    async getActiveModules() {
        try {
            const allModules = await this.getInstalledModules();
            return allModules.filter(module => module.status === 'active');
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to get active modules:', error);
            return [];
        }
    }

    /**
     * Refresh cache
     * @private
     */
    async refreshCache() {
        try {
            // Try JSON persistence first
            if (this.persistenceMode === 'json' && window.electronAPI && window.electronAPI.jsonPersistence) {
                try {
                    const modules = await window.electronAPI.jsonPersistence.getInstalledModules();
                    
                    if (modules && Array.isArray(modules)) {
                        this.updateCache(modules);
                        this.logger.debug('ModulePersistenceClient', `Cache refreshed with ${modules.length} modules from JSON persistence`);
                        return;
                    }
                } catch (jsonError) {
                    this.logger.warn('ModulePersistenceClient', 'Failed to refresh cache from JSON persistence:', jsonError);
                    this.persistenceMode = 'localStorage';
                }
            }

            // localStorage fallback
            if (this.persistenceMode === 'localStorage') {
                await this.loadFromLocalStorage();
                this.logger.debug('ModulePersistenceClient', `Cache refreshed with ${this.moduleCache.size} modules from localStorage`);
            }
        } catch (error) {
            this.logger.error('ModulePersistenceClient', 'Failed to refresh cache:', error);
        }
    }

    /**
     * Update cache with modules array
     * @private
     */
    updateCache(modules) {
        this.moduleCache.clear();
        modules.forEach(module => {
            // Use moduleId for consistency (JSON persistence) or module_id for backward compatibility
            const id = module.moduleId || module.module_id;
            if (id) {
                this.moduleCache.set(id, module);
            }
        });
        this.lastCacheUpdate = Date.now();
    }

    /**
     * Check if cache is valid
     * @private
     */
    isCacheValid() {
        return (Date.now() - this.lastCacheUpdate) < this.cacheExpiry;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.moduleCache.clear();
        this.lastCacheUpdate = 0;
        this.logger.debug('ModulePersistenceClient', 'Cache cleared');
    }
}

// Export for browser environment
if (typeof window !== 'undefined') {
    window.ModulePersistenceClient = ModulePersistenceClient;
}
