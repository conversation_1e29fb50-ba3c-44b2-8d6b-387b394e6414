/* Activity bar styling - ensure no layout conflicts */
.activity-bar {
    width: var(--activitybar-width);
    background: var(--activitybar-background);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    user-select: none;
    /* Ensure tight layout */
    margin: 0;
    padding: 0;
    flex-shrink: 0;
}

.activity-bar-items {
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 4px 0;
}

.activity-bar-bottom {
    display: flex;
    flex-direction: column;
    border-top: 1px solid var(--border-primary);
    margin: 0;
    padding: 4px 0;
}

/* Activity buttons - enhanced specificity to avoid conflicts */
.activity-bar .activity-button {
    width: var(--activitybar-width);
    height: calc(var(--activitybar-width) * 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
    /* Reset button styles */
    background: none;
    border: none;
    cursor: pointer;
    margin: 0;
    padding: 0;
    border-radius: 0;
}

.activity-bar .activity-button:hover {
    background: var(--background-quaternary);
}

.activity-bar .activity-button.active {
    border-left-color: var(--accent-primary);
    background: var(--background-quaternary);
}

.activity-bar .activity-button.active .activity-icon {
    background-color: var(--foreground-primary);
}

/* Activity icons - using mask-image for robust theming */
.activity-bar .activity-icon {
    width: var(--icon-size-md);
    height: var(--icon-size-md);
    background-color: var(--foreground-muted);
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: 100%;
    transition: background-color var(--transition-fast);
}

.activity-bar .activity-button:hover .activity-icon {
    background-color: var(--foreground-primary);
}

/* Icon definitions con fallback */
.icon-explorer {
    mask-image: url('../assets/icons/folder-mask.svg');
}

.icon-cloud {
    mask-image: url('../assets/icons/cloud-mask.svg');
}

.icon-search {
    mask-image: url('../assets/icons/search-mask.svg');
}

.icon-modules {
    mask-image: url('../assets/icons/grid-mask.svg');
}

.icon-extensions {
    mask-image: url('../assets/icons/puzzle-mask.svg');
}

.icon-settings {
    mask-image: url('../assets/icons/settings-mask.svg');
}

/* Fallback usando pseudo-elementos con caracteres Unicode */
.icon-explorer::after {
    content: '📁';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

.icon-cloud::after {
    content: '☁️';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

.icon-search::after {
    content: '🔍';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

.icon-modules::after {
    content: '⚏';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
}

.icon-extensions::after {
    content: '🧩';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

.icon-settings::after {
    content: '⚙️';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    display: none;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

/* Estados hover para iconos de fallback */
.activity-bar .activity-button:hover .icon-explorer::after,
.activity-bar .activity-button:hover .icon-cloud::after,
.activity-bar .activity-button:hover .icon-search::after,
.activity-bar .activity-button:hover .icon-modules::after,
.activity-bar .activity-button:hover .icon-extensions::after,
.activity-bar .activity-button:hover .icon-settings::after {
    color: var(--foreground-primary);
    filter: grayscale(0) brightness(1);
}

.activity-bar .activity-button.active .icon-explorer::after,
.activity-bar .activity-button.active .icon-cloud::after,
.activity-bar .activity-button.active .icon-search::after,
.activity-bar .activity-button.active .icon-modules::after,
.activity-bar .activity-button.active .icon-extensions::after,
.activity-bar .activity-button.active .icon-settings::after {
    color: var(--foreground-primary);
    filter: grayscale(0) brightness(1);
}

/* Activar fallback si mask-image no funciona */
@supports not (mask-image: url('data:image/svg+xml,<svg></svg>')) {
    .activity-icon {
        background-color: transparent !important;
    }

    .icon-explorer::after,
    .icon-cloud::after,
    .icon-search::after,
    .icon-modules::after,
    .icon-extensions::after,
    .icon-settings::after {
        display: block !important;
        color: var(--foreground-muted);
        filter: grayscale(1) brightness(0.8);
    }
}

/* Force fallback activation for testing/debugging */
.activity-bar.force-fallback .activity-icon {
    background-color: transparent !important;
    mask-image: none !important;
}

.activity-bar.force-fallback .icon-explorer::after,
.activity-bar.force-fallback .icon-cloud::after,
.activity-bar.force-fallback .icon-search::after,
.activity-bar.force-fallback .icon-modules::after,
.activity-bar.force-fallback .icon-extensions::after,
.activity-bar.force-fallback .icon-settings::after {
    display: block !important;
    color: var(--foreground-muted);
    filter: grayscale(1) brightness(0.8);
}

    .icon-explorer::after,
    .icon-cloud::after,
    .icon-search::after,
    .icon-modules::after,
    .icon-extensions::after,
    .icon-settings::after {
        display: block;
    }
}

/* Badge/notification indicator */
.activity-button .badge {
    position: absolute;
    top: 8px;
    right: 8px;
    min-width: 16px;
    height: 16px;
    background: var(--accent-primary);
    color: #ffffff;
    border-radius: 8px;
    font-size: 10px;
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-sizing: border-box;
}

.activity-button .badge.dot {
    width: 8px;
    height: 8px;
    min-width: 8px;
    border-radius: 4px;
    top: 12px;
    right: 12px;
}

/* Removed custom tooltips - using native HTML title attribute */

/* Responsive behavior */
@media (max-width: 768px) {
    .activity-bar {
        width: 40px;
    }
    
    .activity-button {
        width: 40px;
        height: 40px;
    }
    
    .activity-icon {
        width: 20px;
        height: 20px;
    }
}

/* Animation for panel switching */
.activity-button.switching {
    animation: activityPulse 0.3s ease;
}

@keyframes activityPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Settings button special styling */
.activity-bar-bottom .activity-button {
    border-top: 1px solid var(--border-secondary);
}

.activity-bar-bottom .activity-button:first-child {
    border-top: none;
}

/* Module-specific button states */
.activity-button[data-module="lexflow"].active {
    border-left-color: #4a90e2;
}

.activity-button[data-module="protocolx"].active {
    border-left-color: #7b68ee;
}

.activity-button[data-module="auditpro"].active {
    border-left-color: #50c878;
}

.activity-button[data-module="finsync"].active {
    border-left-color: #ff6b6b;
}

/* Disabled state for upcoming modules */
.activity-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.activity-button.disabled:hover {
    color: var(--foreground-muted);
    background: transparent;
}

.activity-button.disabled .activity-icon {
    opacity: 0.5;
}

.activity-button.disabled::after {
    content: "Próximamente";
}

/* Explicit theme support for activity bar */
[data-theme="light"] .activity-bar {
    background: var(--activitybar-background);
    border-right-color: var(--border-primary);
}

[data-theme="dark"] .activity-bar {
    background: var(--activitybar-background);
    border-right-color: var(--border-primary);
}

[data-theme="light"] .activity-bar .activity-icon {
    background-color: var(--foreground-muted);
}

[data-theme="dark"] .activity-bar .activity-icon {
    background-color: var(--foreground-muted);
}

[data-theme="light"] .activity-bar .activity-button:hover .activity-icon,
[data-theme="light"] .activity-bar .activity-button.active .activity-icon {
    background-color: var(--foreground-primary);
}

[data-theme="dark"] .activity-bar .activity-button:hover .activity-icon,
[data-theme="dark"] .activity-bar .activity-button.active .activity-icon {
    background-color: var(--foreground-primary);
}