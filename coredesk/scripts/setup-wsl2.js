#!/usr/bin/env node

/**
 * CoreDesk Framework - WSL2 Setup Script
 * Configura automáticamente WSL2 para ejecutar aplicaciones gráficas
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class WSL2Setup {
    constructor() {
        this.isWSL = this.detectWSL();
    }

    /**
     * Detecta si estamos en WSL
     */
    detectWSL() {
        try {
            if (fs.existsSync('/proc/version')) {
                const version = fs.readFileSync('/proc/version', 'utf8');
                return version.toLowerCase().includes('microsoft') || 
                       version.toLowerCase().includes('wsl');
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * Verifica si WSLg está disponible
     */
    checkWSLg() {
        return fs.existsSync('/mnt/wslg');
    }

    /**
     * Instala dependencias necesarias
     */
    async installDependencies() {
        console.log('📦 [Setup] Instalando dependencias para aplicaciones gráficas...');
        
        const packages = [
            'x11-apps',      // Aplicaciones básicas de X11
            'x11-utils',     // Utilidades de X11
            'x11-xserver-utils', // Utilidades del servidor X11
            'dbus-x11',      // D-Bus para X11
            'libgtk-3-0',    // GTK3
            'libxss1',       // Screen saver extension
            'libgconf-2-4',  // GConf
            'libxtst6',      // X11 Testing extension
            'libxrandr2',    // X11 RandR extension
            'libasound2',    // ALSA sound
            'libpangocairo-1.0-0', // Pango Cairo
            'libatk1.0-0',   // ATK accessibility
            'libcairo-gobject2', // Cairo GObject
            'libgtk-3-0',    // GTK3
            'libgdk-pixbuf2.0-0' // GDK Pixbuf
        ];

        return new Promise((resolve, reject) => {
            const aptUpdate = spawn('sudo', ['apt', 'update'], { stdio: 'inherit' });
            
            aptUpdate.on('close', (code) => {
                if (code !== 0) {
                    console.log('⚠️  [Setup] Error actualizando paquetes');
                    resolve(false);
                    return;
                }

                const aptInstall = spawn('sudo', ['apt', 'install', '-y', ...packages], { 
                    stdio: 'inherit' 
                });

                aptInstall.on('close', (installCode) => {
                    if (installCode === 0) {
                        console.log('✅ [Setup] Dependencias instaladas correctamente');
                        resolve(true);
                    } else {
                        console.log('⚠️  [Setup] Error instalando dependencias');
                        resolve(false);
                    }
                });

                aptInstall.on('error', (error) => {
                    console.log('❌ [Setup] Error:', error.message);
                    resolve(false);
                });
            });

            aptUpdate.on('error', (error) => {
                console.log('❌ [Setup] Error:', error.message);
                resolve(false);
            });
        });
    }

    /**
     * Configura variables de entorno
     */
    setupEnvironment() {
        console.log('⚙️  [Setup] Configurando variables de entorno...');
        
        const bashrcPath = path.join(os.homedir(), '.bashrc');
        const envVars = [
            '',
            '# CoreDesk Framework - WSL2 Configuration',
            'export DISPLAY=:0',
            'export LIBGL_ALWAYS_INDIRECT=1',
            'export XDG_RUNTIME_DIR=/tmp/runtime-$USER',
            'export RUNLEVEL=3',
            ''
        ];

        try {
            // Leer .bashrc actual
            let bashrcContent = '';
            if (fs.existsSync(bashrcPath)) {
                bashrcContent = fs.readFileSync(bashrcPath, 'utf8');
            }

            // Verificar si ya está configurado
            if (bashrcContent.includes('CoreDesk Framework - WSL2 Configuration')) {
                console.log('✅ [Setup] Variables de entorno ya configuradas');
                return true;
            }

            // Agregar configuración
            bashrcContent += envVars.join('\n');
            fs.writeFileSync(bashrcPath, bashrcContent);
            
            console.log('✅ [Setup] Variables de entorno configuradas en ~/.bashrc');
            return true;
        } catch (error) {
            console.log('❌ [Setup] Error configurando variables:', error.message);
            return false;
        }
    }

    /**
     * Crea directorio runtime
     */
    setupRuntimeDir() {
        console.log('📁 [Setup] Configurando directorio runtime...');
        
        try {
            const runtimeDir = `/tmp/runtime-${os.userInfo().username}`;
            if (!fs.existsSync(runtimeDir)) {
                fs.mkdirSync(runtimeDir, { recursive: true, mode: 0o700 });
            }
            console.log('✅ [Setup] Directorio runtime configurado');
            return true;
        } catch (error) {
            console.log('❌ [Setup] Error configurando directorio runtime:', error.message);
            return false;
        }
    }

    /**
     * Verifica la configuración
     */
    async verifySetup() {
        console.log('🔍 [Setup] Verificando configuración...');
        
        // Verificar DISPLAY
        const display = process.env.DISPLAY || ':0';
        console.log(`   DISPLAY: ${display}`);
        
        // Verificar WSLg
        if (this.checkWSLg()) {
            console.log('   WSLg: ✅ Disponible');
        } else {
            console.log('   WSLg: ❌ No disponible');
        }

        // Verificar X11
        return new Promise((resolve) => {
            const xdpyinfo = spawn('xdpyinfo', ['-display', display]);
            
            xdpyinfo.on('close', (code) => {
                if (code === 0) {
                    console.log('   X11: ✅ Servidor disponible');
                    resolve(true);
                } else {
                    console.log('   X11: ❌ Servidor no disponible');
                    resolve(false);
                }
            });
            
            xdpyinfo.on('error', () => {
                console.log('   X11: ❌ xdpyinfo no disponible');
                resolve(false);
            });
        });
    }

    /**
     * Ejecuta la configuración completa
     */
    async setup() {
        console.log('🔧 [Setup] Configurando WSL2 para CoreDesk Framework...');
        console.log('');

        if (!this.isWSL) {
            console.log('❌ [Setup] Este script solo funciona en WSL2');
            return false;
        }

        // Verificar WSLg
        if (this.checkWSLg()) {
            console.log('✅ [Setup] WSLg detectado - configuración mínima requerida');
        } else {
            console.log('⚠️  [Setup] WSLg no detectado - configuración completa requerida');
        }

        console.log('');

        // Instalar dependencias
        const depsInstalled = await this.installDependencies();
        if (!depsInstalled) {
            console.log('⚠️  [Setup] Continuando sin todas las dependencias...');
        }

        // Configurar entorno
        this.setupEnvironment();
        this.setupRuntimeDir();

        console.log('');

        // Verificar configuración
        const setupOk = await this.verifySetup();
        
        console.log('');
        
        if (setupOk) {
            console.log('✅ [Setup] Configuración completada exitosamente');
            console.log('');
            console.log('🚀 [Setup] Ahora puedes ejecutar: npm start');
        } else {
            console.log('⚠️  [Setup] Configuración completada con advertencias');
            console.log('');
            console.log('📋 [Setup] Para completar la configuración:');
            console.log('   1. Reinicia tu terminal WSL2');
            console.log('   2. Si usas Windows 10, instala VcXsrv o Xming');
            console.log('   3. Ejecuta: source ~/.bashrc');
            console.log('   4. Intenta: npm start');
        }

        return setupOk;
    }
}

// Ejecutar setup si es llamado directamente
if (require.main === module) {
    const setup = new WSL2Setup();
    setup.setup().catch(error => {
        console.error('❌ [Setup] Error fatal:', error.message);
        process.exit(1);
    });
}

module.exports = WSL2Setup;
