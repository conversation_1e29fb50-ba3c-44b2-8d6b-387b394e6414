/**
 * main.js
 * Main process for CoreDesk Framework v2.0
 * Refactored for improved code quality and reduced complexity
 */

const { app, BrowserWindow, ipcMain, screen } = require('electron');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Import services and utilities
const logger = require('./src/js/utils/MainLogger');
const DatabaseService = require('./src/js/services/DatabaseService');
const JsonPersistenceService = require('./src/js/services/JsonPersistenceService');
const WindowHandlers = require('./src/js/services/WindowHandlers');
const DatabaseHandlers = require('./src/js/services/DatabaseHandlers');
const SystemHandlers = require('./src/js/services/SystemHandlers');
const LicenseHandlers = require('./src/js/services/LicenseHandlers');
const FileSystemHandlers = require('./src/js/services/FileSystemHandlers');
const UpdateManager = require('./src/js/services/UpdateManager');

class CoreDeskApp {
    constructor() {
        this.mainWindow = null;
        this.database = null;
        this.updateManager = null;
        this.handlers = [];
        this.isInitialized = false;
        this.restoreBounds = null; // Store bounds for restore functionality
        this.isWindowMaximized = false; // Track maximize state manually

        // Setup app asynchronously
        this.setupApp().catch((error) => {
            logger.error('CoreDeskApp', 'Failed to setup app', error);
        });
    }

    async setupApp() {
        // Set security policies
        this.configureSecurityPolicies();

        // Set up app event listeners
        this.setupAppEventListeners();

        // Initialize database before IPC handlers
        await this.initializeDatabase();

        // Setup IPC handlers after database is initialized
        this.setupIPCHandlers();
    }

    configureSecurityPolicies() {
        // Suppress D-Bus errors and other Linux-specific warnings
        if (process.platform === 'linux') {
            app.commandLine.appendSwitch('disable-dev-shm-usage');
            app.commandLine.appendSwitch('no-sandbox');
            app.commandLine.appendSwitch('disable-gpu-sandbox');
            app.commandLine.appendSwitch('disable-software-rasterizer');

            // Suppress AppImage warnings
            if (!process.env.APPIMAGE) {
                process.env.APPIMAGE_SILENT = 'true';
            }
        }

        // Disable features that could be security risks
        app.commandLine.appendSwitch('disable-features', 'OutOfBlinkCors');

        // Prevent new window creation
        app.on('web-contents-created', this.handleWebContentsCreated.bind(this));
    }

    setupAppEventListeners() {
        app.whenReady().then(async () => {
            // Database should already be initialized in setupApp()
            logger.info('CoreDeskApp', 'App ready, creating main window...');

            this.createMainWindow();
        });

        app.on('window-all-closed', this.handleWindowAllClosed.bind(this));
        
        app.on('activate', this.handleActivate.bind(this));
    }

    createMainWindow() {
        try {
            this.mainWindow = new BrowserWindow(this.getWindowConfig());
            
            // Prevent window from starting maximized
            if (this.mainWindow.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            
            // Center the window on screen
            this.mainWindow.center();
            
            this.setupWindowEventListeners();
            this.loadMainContent();
            
            // Send database ready event if database is already initialized
            if (this.database && this.database.isInitialized) {
                setTimeout(() => {
                    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                        this.mainWindow.webContents.send('database:ready');
                        logger.info('CoreDeskApp', 'Sent database:ready event to renderer after window creation');
                    }
                }, 100); // Small delay to ensure renderer is ready
            }
            
        } catch (error) {
            logger.error('CoreDeskApp', 'Failed to create main window', error);
        }
    }

    getWindowConfig() {
        // Get primary display to calculate appropriate sizing
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workArea;
        const scaleFactor = primaryDisplay.scaleFactor;
        
        // Calculate optimal window size based on screen size and scale factor
        // Use percentage of screen for better scaling across different displays
        const optimalWidth = Math.floor(Math.min(1400, screenWidth * 0.85));
        const optimalHeight = Math.floor(Math.min(900, screenHeight * 0.85));
        
        // Adjust minimum sizes based on scale factor
        const adjustedMinWidth = Math.floor(1024 / scaleFactor);
        const adjustedMinHeight = Math.floor(768 / scaleFactor);
        
        console.log(`[WindowConfig] Screen: ${screenWidth}x${screenHeight}, Scale: ${scaleFactor}, Optimal: ${optimalWidth}x${optimalHeight}`);
        
        return {
            width: optimalWidth,
            height: optimalHeight,
            minWidth: Math.max(800, adjustedMinWidth),
            minHeight: Math.max(600, adjustedMinHeight),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: path.join(__dirname, 'preload.js'),
                webSecurity: true,
                enableRemoteModule: false,
                devTools: true, // Enable dev tools for debugging
                // Improved scaling and rendering options
                zoomFactor: 1.0, // Start with default zoom
                enableWebSQL: false,
                experimentalFeatures: false
            },
            frame: false,
            titleBarStyle: 'hidden',
            backgroundColor: '#1e1e1e',
            show: true, // Show immediately for WSL2 compatibility
            icon: path.join(__dirname, 'src/assets/icons/app.png'),
            // Ensure window starts in normal state
            alwaysOnTop: false,
            skipTaskbar: false,
            movable: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            fullscreenable: true,
            // Improved display handling
            useContentSize: true, // Use content size instead of window frame size
            autoHideMenuBar: true
        };
    }

    setupWindowEventListeners() {
        this.mainWindow.once('ready-to-show', this.handleWindowReady.bind(this));
        this.mainWindow.on('closed', this.handleWindowClosed.bind(this));
    }

    loadMainContent() {
        this.mainWindow.loadFile('src/index.html');
        
        if (this.isDevelopmentMode()) {
            this.mainWindow.webContents.openDevTools();
        }
    }

    async handleWindowReady() {
        logger.info('CoreDeskApp', 'Window ready event received');
        
        if (this.mainWindow) {
            // Configure optimal scaling based on display properties
            await this.configureWindowScaling();
            
            // Ensure window starts in normal (not maximized) state
            if (this.mainWindow.isMaximized()) {
                this.mainWindow.unmaximize();
                logger.info('CoreDeskApp', 'Window was maximized on startup, unmaximizing to normal state');
            }
            
            logger.info('CoreDeskApp', 'Ensuring window is visible and focused');
            this.mainWindow.show();
            this.mainWindow.focus();
            this.mainWindow.moveTop();
            
            // Log initial window state for debugging
            const primaryDisplay = screen.getPrimaryDisplay();
            const scaleFactor = primaryDisplay.scaleFactor;
            const bounds = this.mainWindow.getBounds();
            
            logger.info('CoreDeskApp', `Initial window state: maximized=${this.mainWindow.isMaximized()}, minimized=${this.mainWindow.isMinimized()}`);
            logger.info('CoreDeskApp', `Display info: scale=${scaleFactor}, window=${bounds.width}x${bounds.height}, screen=${primaryDisplay.workArea.width}x${primaryDisplay.workArea.height}`);
            
            // Open DevTools in development mode
            if (this.isDevelopmentMode()) {
                this.mainWindow.webContents.openDevTools();
                logger.info('CoreDeskApp', 'DevTools opened for debugging');
            }
        }
        
        // Initialize services after a brief delay
        setTimeout(async () => {
            await this.initializeCoreServices();
        }, 200);
    }

    async configureWindowScaling() {
        try {
            const primaryDisplay = screen.getPrimaryDisplay();
            const scaleFactor = primaryDisplay.scaleFactor;
            const platform = process.platform;
            
            // Configure zoom based on platform and scale factor
            let optimalZoom = 1.0;
            
            if (platform === 'win32') {
                // Windows: Adjust zoom based on system scale factor
                if (scaleFactor >= 2.0) {
                    optimalZoom = 0.8; // Scale down for high DPI displays
                } else if (scaleFactor >= 1.5) {
                    optimalZoom = 0.9; // Slight scale down for 1.5x displays
                } else if (scaleFactor >= 1.25) {
                    optimalZoom = 0.95; // Minimal scale down for 1.25x displays
                }
            } else if (platform === 'linux') {
                // Linux/WSL: Different scaling approach
                if (scaleFactor >= 2.0) {
                    optimalZoom = 0.75; // More aggressive scaling for Linux high DPI
                } else if (scaleFactor >= 1.5) {
                    optimalZoom = 0.85;
                } else if (scaleFactor >= 1.25) {
                    optimalZoom = 0.9;
                }
            }
            
            // Apply zoom if different from default
            if (optimalZoom !== 1.0) {
                this.mainWindow.webContents.setZoomFactor(optimalZoom);
                logger.info('CoreDeskApp', `Applied zoom factor: ${optimalZoom} (platform: ${platform}, scale: ${scaleFactor})`);
            }
            
            // Set zoom level limits to prevent users from making UI unusable
            this.mainWindow.webContents.setVisualZoomLevelLimits(1, 3);
            
            // Store scaling info for future use
            this.scalingInfo = {
                scaleFactor,
                platform,
                appliedZoom: optimalZoom,
                screenSize: {
                    width: primaryDisplay.workArea.width,
                    height: primaryDisplay.workArea.height
                }
            };
            
        } catch (error) {
            logger.error('CoreDeskApp', 'Failed to configure window scaling', error);
        }
    }

    handleWindowClosed() {
        this.mainWindow = null;
        this.cleanup();
    }

    handleWindowAllClosed() {
        if (process.platform !== 'darwin') {
            app.quit();
        }
    }

    handleActivate() {
        if (BrowserWindow.getAllWindows().length === 0) {
            this.createMainWindow();
        }
    }

    handleWebContentsCreated(event, contents) {
        // Prevent new window creation
        contents.on('new-window', (event, url) => {
            event.preventDefault();
            logger.warn('CoreDeskApp', 'Blocked new window creation', { url });
        });

        // Prevent navigation to external URLs
        contents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            
            if (parsedUrl.protocol !== 'file:') {
                event.preventDefault();
                logger.warn('CoreDeskApp', 'Blocked navigation to external URL', { url: navigationUrl });
            }
        });
    }

    async initializeCoreServices() {
        try {
            logger.info('CoreDeskApp', 'Initializing core services...');

            // Database should already be initialized in setupApp()
            if (!this.database || !this.database.isInitialized) {
                logger.error('CoreDeskApp', 'Database not initialized - this should not happen!');
                throw new Error('Database initialization failed in setupApp()');
            } else {
                logger.info('CoreDeskApp', 'Database already initialized, proceeding with other services...');
            }

            // Initialize UpdateManager
            await this.initializeUpdateManager();

            // File system handlers are already initialized in setupIPCHandlers

            // Verify license
            const licenseValid = await this.verifyLicense();

            // Send initialization complete event
            this.sendInitializationComplete(licenseValid);
            
            // Send database ready event
            if (this.database && this.database.isInitialized && this.mainWindow && !this.mainWindow.isDestroyed()) {
                this.mainWindow.webContents.send('database:ready');
                logger.info('CoreDeskApp', 'Sent database:ready event after core services initialization');
            }

            this.isInitialized = true;
            logger.info('CoreDeskApp', 'Core services initialized successfully');

        } catch (error) {
            logger.error('CoreDeskApp', 'Failed to initialize core services', error);
            this.sendInitializationError(error);
        }
    }

    async initializeDatabase() {
        try {
            // Prevent multiple initializations
            if (this.database && this.database.isInitialized && this.jsonPersistence && this.jsonPersistence.isInitialized) {
                logger.info('CoreDeskApp', 'Persistence services already initialized, skipping...');
                return true;
            }

            logger.info('CoreDeskApp', 'Initializing persistence services...');

            // Initialize JSON persistence service first
            this.jsonPersistence = new JsonPersistenceService(logger);
            const jsonInitialized = await this.jsonPersistence.initialize();

            if (jsonInitialized) {
                logger.info('CoreDeskApp', 'JSON persistence service initialized successfully');
                global.jsonPersistence = this.jsonPersistence;
            } else {
                logger.warn('CoreDeskApp', 'JSON persistence service initialization failed');
            }

            // Still initialize SQLite as fallback (but prioritize JSON)
            this.database = new DatabaseService(logger);
            const dbInitialized = await this.database.initialize();

            if (dbInitialized) {
                global.database = this.database.getConnection();
                logger.info('CoreDeskApp', 'Database initialized successfully as fallback');
                
                // Verify table was created
                try {
                    const result = await this.database.execute('SELECT name FROM sqlite_master WHERE type="table" AND name="installed_modules"', []);
                    logger.info('CoreDeskApp', `installed_modules table check result:`, result);
                } catch (tableError) {
                    logger.error('CoreDeskApp', 'Error checking installed_modules table:', tableError);
                }
            } else {
                logger.warn('CoreDeskApp', 'Database initialization failed');
                this.database = null;
            }
            
            // Send persistence ready event to all windows
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                this.mainWindow.webContents.send('persistence:ready', {
                    jsonPersistence: jsonInitialized,
                    database: dbInitialized
                });
                logger.info('CoreDeskApp', 'Sent persistence:ready event to renderer');
            }
            
            return jsonInitialized || dbInitialized;

        } catch (error) {
            logger.error('CoreDeskApp', 'Persistence initialization failed', error);
            return false;
        }
    }

    async initializeUpdateManager() {
        try {
            this.updateManager = new UpdateManager();
            
            // Setup event listeners for update events
            this.updateManager.addEventListener('update-available', (event) => {
                const updateInfo = event.detail;
                logger.info('CoreDeskApp', `Update available: v${updateInfo.version}`);
                
                // Send update notification to renderer process
                if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                    this.mainWindow.webContents.send('update-available', updateInfo);
                }
            });

            this.updateManager.addEventListener('update-downloaded', (event) => {
                const updateInfo = event.detail;
                logger.info('CoreDeskApp', 'Update downloaded, ready to install');
                
                // Send update ready notification to renderer process
                if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                    this.mainWindow.webContents.send('update-downloaded', updateInfo);
                }
            });

            this.updateManager.addEventListener('download-progress', (event) => {
                const progress = event.detail;
                
                // Send progress to renderer process
                if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                    this.mainWindow.webContents.send('update-progress', progress);
                }
            });

            this.updateManager.addEventListener('update-error', (event) => {
                const error = event.detail;
                logger.error('CoreDeskApp', 'Update error:', error);
                
                // Send error to renderer process
                if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                    this.mainWindow.webContents.send('update-error', error);
                }
            });
            
            // Make update manager available globally
            global.updateManager = this.updateManager;
            
            logger.info('CoreDeskApp', 'UpdateManager initialized successfully');
            return true;
            
        } catch (error) {
            logger.error('CoreDeskApp', 'UpdateManager initialization failed', error);
            return false;
        }
    }

    setupIPCHandlers() {
        try {
            logger.info('CoreDeskApp', 'Setting up IPC handlers...');
            
            // Clear any existing handlers first to avoid conflicts
            const handlersToRemove = ['window:minimize', 'window:maximize', 'window:close', 'window:move', 'app-get-version', 'app-quit', 'system-get-info', 'db-execute', 'update:check', 'update:download', 'update:install', 'update:get-status', 'update:set-channel'];
            handlersToRemove.forEach(handler => {
                try {
                    ipcMain.removeHandler(handler);
                } catch (e) {
                    // Handler might not exist, which is fine
                }
            });
            
            // Window controls - Fixed channel names to match preload.js
            ipcMain.handle('window:minimize', async (event) => {
                try {
                    logger.debug('CoreDeskApp', 'Minimize window request received');
                    const window = BrowserWindow.getFocusedWindow() || this.mainWindow;
                    if (window && !window.isDestroyed()) {
                        window.minimize();
                        logger.debug('CoreDeskApp', 'Window minimized successfully');
                        return { success: true };
                    }
                    logger.warn('CoreDeskApp', 'No valid window to minimize');
                    return { success: false, error: 'No valid window' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error minimizing window:', error);
                    return { success: false, error: error.message };
                }
            });
            
            ipcMain.handle('window:maximize', async (event) => {
                try {
                    logger.debug('CoreDeskApp', 'Maximize window request received');
                    const window = BrowserWindow.getFocusedWindow() || this.mainWindow;
                    if (window && !window.isDestroyed()) {
                        
                        // Simple approach: track maximize state manually
                        if (!this.isWindowMaximized) {
                            // Store current bounds before maximizing
                            this.restoreBounds = window.getBounds();
                            
                            // Get display and work area
                            const currentBounds = window.getBounds();
                            const currentDisplay = screen.getDisplayNearestPoint(currentBounds);
                            const workArea = currentDisplay.workArea;
                            
                            // Maximize to work area
                            window.setBounds({
                                x: workArea.x,
                                y: workArea.y,
                                width: workArea.width,
                                height: workArea.height
                            });
                            
                            this.isWindowMaximized = true;
                            
                            // Send maximized state to renderer
                            if (window.webContents) {
                                window.webContents.send('window:maximized', true);
                            }
                            
                            logger.debug('CoreDeskApp', 'Window maximized');
                            return { success: true, maximized: true, action: 'maximized' };
                            
                        } else {
                            // Restore from maximized state
                            if (this.restoreBounds) {
                                window.setBounds(this.restoreBounds);
                            } else {
                                // Fallback to centered default size
                                const currentDisplay = screen.getDisplayNearestPoint(window.getBounds());
                                const workArea = currentDisplay.workArea;
                                window.setBounds({
                                    width: 1400,
                                    height: 900,
                                    x: Math.floor((workArea.width - 1400) / 2 + workArea.x),
                                    y: Math.floor((workArea.height - 900) / 2 + workArea.y)
                                });
                            }
                            
                            this.isWindowMaximized = false;
                            this.restoreBounds = null;
                            
                            // Send restored state to renderer
                            if (window.webContents) {
                                window.webContents.send('window:maximized', false);
                            }
                            
                            logger.debug('CoreDeskApp', 'Window restored');
                            return { success: true, maximized: false, action: 'restored' };
                        }
                    }
                    logger.warn('CoreDeskApp', 'No valid window to maximize/restore');
                    return { success: false, error: 'No valid window' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error maximizing window:', error);
                    return { success: false, error: error.message };
                }
            });
            
            ipcMain.handle('window:close', async (event) => {
                try {
                    logger.debug('CoreDeskApp', 'Close window request received');
                    const window = BrowserWindow.getFocusedWindow() || this.mainWindow;
                    if (window && !window.isDestroyed()) {
                        window.close();
                        logger.debug('CoreDeskApp', 'Window close requested successfully');
                        return { success: true };
                    }
                    logger.warn('CoreDeskApp', 'No valid window to close');
                    return { success: false, error: 'No valid window' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error closing window:', error);
                    return { success: false, error: error.message };
                }
            });
            
            // Window move handler for JavaScript-based dragging
            ipcMain.handle('window:move', async (event, deltaX, deltaY) => {
                try {
                    logger.debug('CoreDeskApp', `Window move request: deltaX=${deltaX}, deltaY=${deltaY}`);
                    const window = BrowserWindow.getFocusedWindow() || this.mainWindow;
                    if (window && !window.isDestroyed()) {
                        const currentBounds = window.getBounds();
                        const newX = currentBounds.x + deltaX;
                        const newY = currentBounds.y + deltaY;
                        
                        // Get screen bounds to prevent window from going off-screen
                        const currentDisplay = screen.getDisplayNearestPoint(currentBounds);
                        if (currentDisplay) {
                            const workArea = currentDisplay.workArea;
                            
                            // Constrain to screen bounds
                            const constrainedX = Math.max(workArea.x, Math.min(newX, workArea.x + workArea.width - currentBounds.width));
                            const constrainedY = Math.max(workArea.y, Math.min(newY, workArea.y + workArea.height - currentBounds.height));
                            
                            window.setPosition(constrainedX, constrainedY);
                            logger.debug('CoreDeskApp', `Window moved to: ${constrainedX}, ${constrainedY}`);
                            return { success: true, x: constrainedX, y: constrainedY };
                        } else {
                            // Fallback if no display found
                            window.setPosition(newX, newY);
                            logger.debug('CoreDeskApp', `Window moved to (fallback): ${newX}, ${newY}`);
                            return { success: true, x: newX, y: newY };
                        }
                    }
                    logger.warn('CoreDeskApp', 'No valid window for move operation');
                    return { success: false, error: 'No valid window' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error moving window:', error);
                    return { success: false, error: error.message };
                }
            });
            
            logger.info('CoreDeskApp', 'Window move handler registered successfully');
            
            // App controls
            ipcMain.handle('app-get-version', () => {
                return app.getVersion();
            });
            
            ipcMain.handle('app-quit', () => {
                app.quit();
            });
            
            // System info
            ipcMain.handle('system-get-info', () => {
                return {
                    platform: process.platform,
                    version: process.version,
                    arch: process.arch
                };
            });
            
            // Database handlers with retry mechanism
            ipcMain.handle('db-execute', async (event, query, params) => {
                try {
                    logger.info('CoreDeskApp', `Database query received: ${query.substring(0, 50)}...`);

                    // Wait for database to be available with retry mechanism
                    const maxRetries = 50;
                    const retryDelay = 100; // 100ms (50 retries = 5 seconds total)

                    for (let attempt = 0; attempt < maxRetries; attempt++) {
                        if (this.database && this.database.isInitialized) {
                            logger.info('CoreDeskApp', `Database available on attempt ${attempt + 1}, executing query`);
                            const result = await this.database.execute(query, params);
                            logger.info('CoreDeskApp', `Database query completed successfully`);
                            return result;
                        }

                        if (attempt < maxRetries - 1) {
                            logger.info('CoreDeskApp', `Database not ready, waiting... (attempt ${attempt + 1}/${maxRetries}) - database: ${!!this.database}, isInitialized: ${this.database?.isInitialized}`);
                            await new Promise(resolve => setTimeout(resolve, retryDelay));
                        }
                    }

                    // If we get here, database is still not available after all retries
                    logger.error('CoreDeskApp', `Database not available after maximum retries - database: ${!!this.database}, isInitialized: ${this.database?.isInitialized}`);
                    return {
                        success: false,
                        error: 'Database not initialized',
                        data: []
                    };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Database execute error', error);
                    return {
                        success: false,
                        error: error.message,
                        data: []
                    };
                }
            });

            // Update handlers
            ipcMain.handle('update:check', async () => {
                try {
                    if (this.updateManager) {
                        const result = await this.updateManager.checkForUpdates();
                        return { success: true, result };
                    }
                    return { success: false, error: 'UpdateManager not initialized' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error checking for updates:', error);
                    return { success: false, error: error.message };
                }
            });

            ipcMain.handle('update:download', async () => {
                try {
                    if (this.updateManager) {
                        const result = await this.updateManager.downloadAndInstall();
                        return { success: true, result };
                    }
                    return { success: false, error: 'UpdateManager not initialized' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error downloading update:', error);
                    return { success: false, error: error.message };
                }
            });

            ipcMain.handle('update:install', async () => {
                try {
                    if (this.updateManager) {
                        const result = this.updateManager.restartAndInstall();
                        return { success: true, result };
                    }
                    return { success: false, error: 'UpdateManager not initialized' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error installing update:', error);
                    return { success: false, error: error.message };
                }
            });

            ipcMain.handle('update:get-status', async () => {
                try {
                    if (this.updateManager) {
                        const status = this.updateManager.getStatus();
                        return { success: true, status };
                    }
                    return { success: false, error: 'UpdateManager not initialized' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error getting update status:', error);
                    return { success: false, error: error.message };
                }
            });

            ipcMain.handle('update:set-channel', async (event, channel) => {
                try {
                    if (this.updateManager) {
                        this.updateManager.setChannel(channel);
                        return { success: true };
                    }
                    return { success: false, error: 'UpdateManager not initialized' };
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error setting update channel:', error);
                    return { success: false, error: error.message };
                }
            });

            // JSON Persistence handlers
            ipcMain.handle('json-persistence:register-module', async (event, moduleData) => {
                try {
                    logger.info('CoreDeskApp', `JSON persistence register request: ${moduleData?.moduleId}`);
                    logger.info('CoreDeskApp', `JSON persistence service available: ${!!this.jsonPersistence}`);
                    logger.info('CoreDeskApp', `JSON persistence initialized: ${this.jsonPersistence?.isInitialized}`);
                    
                    if (this.jsonPersistence && this.jsonPersistence.isInitialized) {
                        const result = await this.jsonPersistence.registerModule(moduleData);
                        logger.info('CoreDeskApp', `JSON persistence register result: ${result}`);
                        return result;
                    }
                    logger.warn('CoreDeskApp', 'JSON persistence service not available for module registration');
                    return false;
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error registering module in JSON persistence:', error);
                    return false;
                }
            });

            ipcMain.handle('json-persistence:get-installed-modules', async (event) => {
                try {
                    if (this.jsonPersistence && this.jsonPersistence.isInitialized) {
                        const modules = await this.jsonPersistence.getInstalledModules();
                        return modules;
                    }
                    return [];
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error getting installed modules from JSON persistence:', error);
                    return [];
                }
            });

            ipcMain.handle('json-persistence:get-module', async (event, moduleId) => {
                try {
                    if (this.jsonPersistence && this.jsonPersistence.isInitialized) {
                        const module = await this.jsonPersistence.getModule(moduleId);
                        return module;
                    }
                    return null;
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error getting module from JSON persistence:', error);
                    return null;
                }
            });

            ipcMain.handle('json-persistence:unregister-module', async (event, moduleId) => {
                try {
                    if (this.jsonPersistence && this.jsonPersistence.isInitialized) {
                        const result = await this.jsonPersistence.unregisterModule(moduleId);
                        return result;
                    }
                    return false;
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error unregistering module from JSON persistence:', error);
                    return false;
                }
            });

            ipcMain.handle('json-persistence:get-stats', async (event) => {
                try {
                    if (this.jsonPersistence) {
                        const stats = await this.jsonPersistence.getStats();
                        return stats;
                    }
                    return null;
                } catch (error) {
                    logger.error('CoreDeskApp', 'Error getting JSON persistence stats:', error);
                    return null;
                }
            });
            
            // Initialize file system handlers AFTER window controls are set up
            this.fileSystemHandlers = new FileSystemHandlers(logger);
            this.handlers.push(this.fileSystemHandlers);
            
            // Add debugging for unhandled IPC calls
            const originalHandle = ipcMain.handle;
            const registeredHandlers = new Set();
            
            // Track registered handlers
            ['window:minimize', 'window:maximize', 'window:close', 'window:move', 'app-get-version', 'app-quit', 'system-get-info', 'db-execute'].forEach(channel => {
                registeredHandlers.add(channel);
            });
            
            logger.info('CoreDeskApp', `IPC handlers setup completed. Registered handlers: ${Array.from(registeredHandlers).join(', ')}`);
            
            // Verify window:move handler is registered (Note: ipcMain.handle doesn't show up in listenerCount)
            setTimeout(() => {
                logger.info('CoreDeskApp', 'window:move handler should be registered and available for invocation');
            }, 100);
            
        } catch (error) {
            logger.error('CoreDeskApp', 'Failed to setup IPC handlers', error);
        }
    }

    async verifyLicense() {
        try {
            // TODO: Implement actual license verification
            logger.debug('CoreDeskApp', 'License verification - placeholder implementation');
            return false; // Will trigger trial flow
            
        } catch (error) {
            logger.error('CoreDeskApp', 'License verification failed', error);
            return false;
        }
    }

    sendInitializationComplete(licenseValid) {
        if (this.mainWindow && this.mainWindow.webContents) {
            logger.info('CoreDeskApp', 'Sending initialization complete event to renderer');
            this.mainWindow.webContents.send('app:initialized', {
                licenseValid,
                version: app.getVersion(),
                platform: process.platform,
                userDataPath: app.getPath('userData')
            });
        } else {
            logger.warn('CoreDeskApp', 'Cannot send initialization event - window not ready');
        }
    }

    sendInitializationError(error) {
        if (this.mainWindow && this.mainWindow.webContents) {
            this.mainWindow.webContents.send('app:initialization-error', {
                error: error.message
            });
        }
    }

    isDevelopmentMode() {
        return process.argv.includes('--dev');
    }

    cleanup() {
        // Cleanup handlers
        if (this.handlers) {
            this.handlers.forEach(handler => {
                if (handler.cleanup) {
                    handler.cleanup();
                }
            });
            this.handlers = [];
        }

        // Close persistence services
        if (this.jsonPersistence) {
            // JSON persistence doesn't need explicit closing
            this.jsonPersistence = null;
        }
        
        // Close database connection
        if (this.database) {
            this.database.close();
            this.database = null;
        }

        logger.info('CoreDeskApp', 'Application cleanup completed');
    }

    // Public method to get app status
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            hasMainWindow: !!this.mainWindow,
            hasDatabaseConnection: !!this.database,
            handlerCount: this.handlers.length
        };
    }
}

// Create and initialize the application
const coreDeskApp = new CoreDeskApp();

// Export for testing purposes
module.exports = coreDeskApp;