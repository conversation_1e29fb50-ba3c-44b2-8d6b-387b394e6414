/**
 * Test JSON persistence service directly
 */

// Mock electron app for testing
const path = require('path');
const os = require('os');

// Mock electron before requiring JsonPersistenceService
const mockElectron = {
    app: {
        getPath: (name) => {
            if (name === 'userData') {
                return path.join(os.homedir(), '.config', 'coredesk');
            }
            return '';
        }
    }
};

// Install mock in require cache
require.cache[require.resolve('electron')] = {
    exports: mockElectron
};

const JsonPersistenceService = require('./src/js/services/JsonPersistenceService');

// Simple logger
const logger = {
    info: (tag, msg, data) => console.log(`[INFO] [${tag}] ${msg}`, data || ''),
    debug: (tag, msg, data) => console.log(`[DEBUG] [${tag}] ${msg}`, data || ''),
    warn: (tag, msg, data) => console.log(`[WARN] [${tag}] ${msg}`, data || ''),
    error: (tag, msg, data) => console.log(`[ERROR] [${tag}] ${msg}`, data || '')
};

async function testJsonPersistence() {
    console.log('🧪 Testing JSON Persistence Service');
    
    try {
        // Create service instance
        const jsonPersistence = new JsonPersistenceService(logger);
        
        // Initialize
        const initialized = await jsonPersistence.initialize();
        console.log(`✅ Initialization result: ${initialized}`);
        
        if (!initialized) {
            console.log('❌ Failed to initialize JSON persistence');
            return;
        }
        
        // Test module registration
        const testModule = {
            moduleId: 'test-module',
            name: 'Test Module',
            version: '1.0.0',
            installPath: '/test/path',
            manifestData: { name: 'Test Module', version: '1.0.0' },
            status: 'active'
        };
        
        console.log('📝 Registering test module...');
        const registered = await jsonPersistence.registerModule(testModule);
        console.log(`✅ Registration result: ${registered}`);
        
        // Test getting modules
        console.log('📋 Getting installed modules...');
        const modules = await jsonPersistence.getInstalledModules();
        console.log(`✅ Found ${modules.length} modules:`, modules);
        
        // Test getting specific module
        console.log('🔍 Getting specific module...');
        const module = await jsonPersistence.getModule('test-module');
        console.log(`✅ Module found:`, module);
        
        // Test stats
        console.log('📊 Getting stats...');
        const stats = await jsonPersistence.getStats();
        console.log(`✅ Stats:`, stats);
        
        // Clean up test module
        console.log('🗑️ Cleaning up test module...');
        const unregistered = await jsonPersistence.unregisterModule('test-module');
        console.log(`✅ Cleanup result: ${unregistered}`);
        
        console.log('🎉 JSON Persistence test completed successfully!');
        
    } catch (error) {
        console.error('💥 Test failed:', error);
    }
}

// Run test
testJsonPersistence();