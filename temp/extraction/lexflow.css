/**
 * LexFlow Module Package Styles
 * Dynamic module styling for legal case management
 */

/* ===================================
   LEXFLOW MODULE LAYOUT
   =================================== */

.lexflow-module {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--background-primary);
    color: var(--foreground-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    position: relative;
    flex: 1;
}

.lexflow-layout {
    display: flex;
    height: 100%;
    overflow: hidden;
}

/* ===================================
   SIDEBAR NAVIGATION
   =================================== */

.lexflow-sidebar {
    width: 280px;
    min-width: 280px;
    background: var(--background-secondary);
    border-right: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-primary);
}

.sidebar-header h2 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.module-description {
    margin: 0;
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

.sidebar-nav {
    flex: 1;
    padding: 16px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    text-decoration: none;
    color: var(--foreground-secondary);
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.nav-item:hover {
    background: var(--background-hover);
    color: var(--foreground-primary);
}

.nav-item.active {
    background: var(--accent-color);
    color: white;
}

.nav-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--accent-secondary);
}

.nav-icon {
    font-size: 1.125rem;
    margin-right: 12px;
    min-width: 20px;
}

.nav-text {
    flex: 1;
    font-weight: 500;
}

.nav-count {
    background: var(--background-tertiary);
    color: var(--foreground-primary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.nav-item.active .nav-count {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ===================================
   MAIN CONTENT AREA
   =================================== */

.lexflow-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--background-primary);
}

#lexflow-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.view-content {
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

/* ===================================
   VIEW HEADERS
   =================================== */

.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-primary);
}

.view-header h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

/* ===================================
   DASHBOARD VIEW
   =================================== */

.dashboard-header {
    margin-bottom: 32px;
}

.dashboard-header h1 {
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 700;
    color: var(--foreground-primary);
}

.dashboard-header p {
    margin: 0;
    font-size: 1rem;
    color: var(--foreground-secondary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 24px;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    background: var(--background-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 16px;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground-primary);
}

.stat-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--foreground-secondary);
    font-weight: 500;
}

.recent-activity {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 24px;
}

.recent-activity h2 {
    margin: 0 0 20px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.cases-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.case-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--background-primary);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.case-item:hover {
    background: var(--background-hover);
    border-color: var(--border-primary);
}

.case-info h4 {
    margin: 0 0 4px 0;
    font-size: 0.975rem;
    font-weight: 600;
    color: var(--foreground-primary);
}

.case-info p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--foreground-secondary);
}

.case-status {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* ===================================
   CASES VIEW
   =================================== */

.filters-section {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 300px;
}

.search-box input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-secondary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
}

.search-box input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.2);
}

.filter-controls {
    display: flex;
    gap: 12px;
}

.filter-controls select {
    padding: 12px 16px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--background-secondary);
    color: var(--foreground-primary);
    font-size: 0.875rem;
    cursor: pointer;
}

.cases-table {
    background: var(--background-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    overflow: hidden;
}

.cases-table table {
    width: 100%;
    border-collapse: collapse;
}

.cases-table th {
    background: var(--background-tertiary);
    padding: 16px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--foreground-primary);
    border-bottom: 1px solid var(--border-primary);
}

.cases-table td {
    padding: 16px 12px;
    border-bottom: 1px solid var(--border-secondary);
    font-size: 0.875rem;
    color: var(--foreground-primary);
}

.cases-table tr:last-child td {
    border-bottom: none;
}

.cases-table tr:hover {
    background: var(--background-hover);
}

/* ===================================
   STATUS AND PRIORITY BADGES
   =================================== */

.status-badge, .priority-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-active {
    background: #22c55e;
    color: white;
}

.status-badge.status-completed {
    background: #6366f1;
    color: white;
}

.status-badge.status-pending {
    background: #f59e0b;
    color: white;
}

.priority-badge.priority-urgent {
    background: #ef4444;
    color: white;
}

.priority-badge.priority-high {
    background: #f97316;
    color: white;
}

.priority-badge.priority-medium {
    background: #eab308;
    color: white;
}

.priority-badge.priority-low {
    background: #84cc16;
    color: white;
}

/* ===================================
   BUTTONS
   =================================== */

.btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 8px;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background: var(--accent-color-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--background-tertiary);
    color: var(--foreground-primary);
    border: 1px solid var(--border-primary);
}

.btn-secondary:hover {
    background: var(--background-hover);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8125rem;
}

/* ===================================
   PLACEHOLDER CONTENT
   =================================== */

.documents-grid, .calendar-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.document-item, .calendar-placeholder {
    text-align: center;
    padding: 40px;
    background: var(--background-secondary);
    border: 2px dashed var(--border-primary);
    border-radius: 8px;
    max-width: 400px;
}

.document-icon, .calendar-icon {
    font-size: 3rem;
    opacity: 0.6;
    margin-bottom: 16px;
}

.document-item h4, .calendar-placeholder h3 {
    margin: 0 0 8px 0;
    color: var(--foreground-primary);
}

.document-item p, .calendar-placeholder p {
    margin: 0;
    color: var(--foreground-secondary);
}

/* ===================================
   RESPONSIVE DESIGN
   =================================== */

@media (max-width: 768px) {
    .lexflow-layout {
        flex-direction: column;
    }
    
    .lexflow-sidebar {
        width: 100%;
        min-width: unset;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-section {
        flex-direction: column;
    }
    
    .search-box {
        min-width: unset;
    }
    
    .cases-table {
        overflow-x: auto;
    }
    
    .cases-table table {
        min-width: 600px;
    }
}

/* ===================================
   DARK THEME OVERRIDES
   =================================== */

[data-theme="dark"] .lexflow-module {
    /* Dark theme specific styles if needed */
}

/* ===================================
   LIGHT THEME OVERRIDES
   =================================== */

[data-theme="light"] .lexflow-module {
    /* Light theme specific styles if needed */
}