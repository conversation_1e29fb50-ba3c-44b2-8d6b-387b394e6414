/**
 * LexFlow Package Installer
 * Installs the LexFlow module into the dynamic module system
 */

// Auto-installation disabled - modules must be downloaded from server
// setTimeout(async function installLexFlowPackage() {
    try {
        console.log('LexFlow Installer', 'Starting LexFlow package installation...');
        
        // Wait for dynamic module system to be available
        if (!window.DynamicModuleManager || !window.ModuleRegistry || !window.exclusiveModuleController?.dynamicModuleManager) {
            console.warn('LexFlow Installer', 'Dynamic module system not available, waiting...');
            
            // Wait up to 10 seconds for the system to load
            let attempts = 0;
            while ((!window.DynamicModuleManager || !window.ModuleRegistry || !window.exclusiveModuleController?.dynamicModuleManager) && attempts < 100) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.DynamicModuleManager || !window.ModuleRegistry || !window.exclusiveModuleController?.dynamicModuleManager) {
                throw new Error('Dynamic module system not available after waiting');
            }
        }
        
        // Load manifest (embedded to avoid fetch)
        const manifest = {
            "id": "lexflow",
            "name": "<PERSON><PERSON><PERSON>",
            "version": "2.0.0",
            "description": "Gestión completa de casos legales",
            "main": "LexFlowPackage.js",
            "style": "lexflow.css",
            "author": "CoreDesk Team",
            "license": "Commercial",
            "dependencies": [],
            "requiredLicense": "premium",
            "category": "legal",
            "icon": "📄",
            "keywords": ["legal", "casos", "documentos"]
        };
        
        // Load module code by importing the class directly
        if (!window.LexFlowPackage) {
            console.error('LexFlow Installer', 'LexFlowPackage class not available');
            throw new Error('LexFlowPackage class not loaded');
        }
        
        // Create dummy module code (will be loaded from class)
        const moduleCode = 'window.LexFlowPackage';
        
        // Styles will be loaded from CSS file when module activates
        const styles = 'css/modules/lexflow.css';
        
        // Create module package
        const packageData = {
            manifest: manifest,
            moduleCode: moduleCode,
            styles: styles,
            assets: {}
        };
        
        const modulePackage = new window.LexFlowPackage(packageData);
        
        // Get global instances (fallback if not passed in)
        const moduleManager = window.exclusiveModuleController?.dynamicModuleManager;
        
        if (!moduleManager) {
            console.warn('LexFlow Installer', 'No module manager available, creating standalone package');
            
            // Initialize the package directly
            await modulePackage.initialize();
            
            // Make it available globally for testing
            window.lexflowPackage = modulePackage;
            
            console.log('LexFlow Installer', 'LexFlow package installed as standalone');
            return;
        }
        
        // Install through the dynamic module system
        const success = await moduleManager.installModulePackage(modulePackage);
        
        if (success) {
            console.log('LexFlow Installer', 'LexFlow package installed successfully');
            
            // Emit installation event
            if (window.CoreDeskEvents) {
                window.CoreDeskEvents.emit('moduleInstalled', {
                    moduleId: 'lexflow',
                    version: manifest.version,
                    timestamp: new Date().toISOString()
                });
            }
        } else {
            throw new Error('Failed to install LexFlow package');
        }
        
    } catch (error) {
        console.error('LexFlow Installer', 'Installation failed:', error);
        
        // Fallback: Try to install as legacy module
        try {
            console.log('LexFlow Installer', 'Attempting fallback installation...');
            
            // Create an instance and make it available globally
            if (window.LexFlowPackage) {
                const manifest = {
                    id: 'lexflow',
                    name: 'LexFlow',
                    version: '2.0.0',
                    description: 'Gestión completa de casos legales',
                    main: 'LexFlowPackage.js'
                };
                
                const fallbackPackageData = {
                    manifest: manifest,
                    moduleCode: '',
                    styles: '',
                    assets: {}
                };
                window.lexflowPackage = new window.LexFlowPackage(fallbackPackageData);
                await window.lexflowPackage.initialize();
                
                console.log('LexFlow Installer', 'LexFlow package installed as fallback');
            } else {
                console.error('LexFlow Installer', 'LexFlowPackage class not available for fallback');
            }
            
        } catch (fallbackError) {
            console.error('LexFlow Installer', 'Fallback installation also failed:', fallbackError);
        }
    }
// }, 2000); // Auto-installation disabled

console.log('LexFlow Installer', 'LexFlow installer script loaded');