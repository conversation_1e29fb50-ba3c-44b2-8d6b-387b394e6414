# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Standard Workflow
1. First think through the problem, read the codebase for relevant files, and write a plan to todo.md.
2. The plan should have a list of todo items that you can check off as you complete them
3. Before you begin working, check in with me and I will verify the plan.
4. Then, begin working on the todo items, marking them as complete as you go.
5. Please every step of the way just give me a high level explanation of what changes you made
6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.
7. Finally, add a review section to the todo.md file with a summary of the changes you made and any other relevant information.

## Development Commands

### API Server (Backend)
```bash
cd api
npm run dev          # Development with nodemon
npm run start        # Production start
npm run test         # Run all tests
npm run test:unit    # Unit tests only
npm run test:integration # Integration tests only
npm run test:coverage # Tests with coverage report
npm run lint         # ESLint code quality check
```

### Admin Panel (React)
```bash
cd adminPanel
npm run dev          # Vite development server
npm run build        # Production build
npm run preview      # Preview production build
npm run lint         # ESLint
npm run lint:fix     # ESLint with auto-fix
npm run test         # Vitest tests
npm run test:coverage # Tests with coverage
```

### Portal
```bash
cd portal
npm run dev          # Development with nodemon
npm run build        # Build CSS from SCSS
npm run build:css    # Compile SCSS to CSS
npm run test         # Jest tests
npm run lint         # ESLint
```

### Admin Panel (Legacy EJS)
```bash
cd admin
npm run dev          # Development with nodemon
npm run start        # Production start
npm run test         # Jest tests
npm run lint         # ESLint
```

### Docker Operations
```bash
docker-compose up -d # Start all services
docker-compose down  # Stop all services
docker-compose logs api # View API logs
docker-compose exec api node src/scripts/initHybridDb.js # Initialize database
```

## Architecture Overview

This is a comprehensive license management system built with a microservices architecture:

### Core Components

1. **API Server** (`/api`) - Express.js RESTful API
   - MongoDB with Mongoose ODM
   - JWT authentication with refresh tokens
   - Swagger documentation at `/api-docs`
   - Firebase integration for cloud storage
   - Comprehensive test suite (unit + integration)
   - Rate limiting and security middleware

2. **Admin Panel React** (`/adminPanel`) - Modern React dashboard
   - Vite build system
   - Material-UI components
   - Zustand state management
   - React Query for data fetching
   - Chart.js for analytics

3. **Portal** (`/portal`) - Public-facing website
   - Express.js with EJS templating
   - SCSS compilation
   - Download and update services

4. **Legacy Admin Panel** (`/admin`) - EJS-based admin interface
   - Express.js with EJS templates
   - Bootstrap styling
   - Chart.js integration

### Database Architecture

- **Primary**: MongoDB (containerized)
- **Models**: License, User, Application, Activation, SecurityEvent, TrialRecord
- **Firebase**: Cloud storage integration for backups
- **Mongo Express**: Web UI for database management (port 8081)

### API Structure

All API endpoints use `/v1` prefix (NOT `/api`):
- Authentication: `/v1/auth/*`
- Licenses: `/v1/licenses/*` 
- Users: `/v1/users/*`
- Activations: `/v1/activations/*`
- Applications: `/v1/applications/*`
- LexFlow module: `/v1/lexflow/*`
- ProtocolX module: `/v1/protocolx/*`

### Key Features

- **Trial License Flow**: 4-step verification process for trial licenses
- **Anti-fraud System**: Email validation and eligibility checks
- **Backup System**: Automated data backup with Google Cloud Storage
- **Security Events**: Comprehensive audit logging
- **Hybrid Database**: Supports both MongoDB and Firebase operations
- **Rate Limiting**: Production-ready API protection

### Port Configuration

- API Server: 3010
- Admin Panel React: 3011 (in Docker), 3020 (local dev)
- Portal: 3000
- Legacy Admin: 3020
- MongoDB: 27017
- Mongo Express: 8081
- Nginx Proxy: 88

### Important Notes

- **No Mock Data**: This is a legal application - only use real data
- **Root Cause Fixes**: Avoid quick fixes or temporary patches
- **Spanish Communication**: Project documentation and communication in Spanish
- **Docker First**: Use Docker services for MongoDB and API development
- **Security**: Never commit credentials; use environment variables

### Testing Strategy

- **Unit Tests**: Individual component testing
- **Integration Tests**: Full API endpoint testing
- **Coverage Reports**: Available via Jest
- **Test Database**: MongoDB Memory Server for isolated testing

### Build and Deployment

The system uses Docker Compose for orchestration with:
- Multi-stage Docker builds
- Environment-specific configurations
- Volume mounting for development
- Health checks for services
- Nginx reverse proxy configuration

### Production Server Access

- **SSH Connection**: There is an established SSH connection to the production server
- **SSH Host**: `coredesk-prod` (configured in `~/.ssh/config`)
- **Server Details**: 
  - Hostname: `**************`
  - User: `administrator`
  - Key: `~/.ssh/coredesk_rsa`
- **Connection Command**: `ssh coredesk-prod`
- **Deployment**: Files can be deployed directly to the production server via SSH
- **Module Distribution**: The portal at `https://coredeskpro.com` serves as the primary distribution point for CoreDesk modules